#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// �������ͱ�ʶ��
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 摄像头分辨率参数定义
// 用于将红色目标追踪到摄像头中心位置
#define CAMERA_WIDTH    640                    // 摄像头宽度
#define CAMERA_HEIGHT   480                    // 摄像头高度
#define CAMERA_CENTER_X (CAMERA_WIDTH / 2)     // 摄像头中心X坐标 = 320
#define CAMERA_CENTER_Y (CAMERA_HEIGHT / 2)    // 摄像头中心Y坐标 = 240

// �����������ݽṹ
typedef struct {
    char type;    // ��������: 'R'��ʾ��ɫ���⣬'G'��ʾ��ɫ����
    int x;        // X����
    int y;        // Y����
    uint8_t isValid; // ������ָʾ��ǰ�����Ƿ���Ч/�Ѹ���
} LaserCoord_t;

int pi_parse_data(char *buffer);
void pi_proc(void);

extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;

#endif
